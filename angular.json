{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"HMS": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/hms", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "/*", "input": "src/app/prescription-management/img/delete.png", "output": "img"}, {"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/bootstrap-icons/font/bootstrap-icons.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "allowedCommonJsDependencies": ["canvg", "core-js", "@babel/runtime", "raf", "rgbcolor"], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4MB", "maximumError": "6MB"}, {"type": "anyComponentStyle", "maximumWarning": "150kB", "maximumError": "350kB"}, {"type": "bundle", "name": "vendor", "maximumWarning": "3MB", "maximumError": "5MB"}, {"type": "any", "maximumWarning": "500kB", "maximumError": "1MB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "HMS:build:production"}, "development": {"buildTarget": "HMS:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "/*", "input": "public"}], "styles": ["src/styles.css", "./node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": ["./node_modules/bootstrap/dist/js/bootstrap.min.js"]}}}}}, "cli": {"analytics": false}}